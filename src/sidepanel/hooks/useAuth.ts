import { useState, useEffect } from 'react'
import { userStorage } from '@src/common/utils'
import type { UserInfo } from '@src/common/utils'
import { checkLoginStatus } from '../utils'

export const useAuth = () => {
  const [isCompleted, setIsCompleted] = useState(false)

  useEffect(() => {
    // 页面加载时检查登录状态
    checkLoginStatus(async (isLoggedIn, data) => {
      if (isLoggedIn && data) {
        // 保存到本地存储
        await userStorage.setUserInfo(data)
        setIsCompleted(true)
      }
    })

    // 监听来自 background 的登录成功消息
    const messageListener = async (message, sender, sendResponse) => {
      if (message.type === 'loginSuccess') {
        checkLoginStatus(async (isLoggedIn, data) => {
          if (isLoggedIn && data) {
            // 保存到本地存储
            await userStorage.setUserInfo(data)
            setIsCompleted(true)
            console.log('登录成功，更新用户信息:', data)
            
            // 关闭登录 tab
            if (message.payload && message.payload.tabId) {
              chrome.tabs.remove(message.payload.tabId)
            }
          }
        })
      }
    }

    chrome.runtime.onMessage.addListener(messageListener)

    // 清理监听器
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener)
    }
  }, [])

  return { isCompleted }
}